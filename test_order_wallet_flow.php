<?php

/**
 * Test script to verify order flow and wallet payment handling
 * This script tests various scenarios to ensure the original order flow is intact
 * and new wallet payment logic works correctly
 */

class OrderWalletFlowTest
{
    private $baseUrl = 'http://127.0.0.1:8003/api/v2/order-management';
    private $token;
    
    public function __construct()
    {
        $this->token = 'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJVa29NTHlmWkRXQ1k2TkdQQy1yMEtQb09kdURDd21KQkdjaVdwenFzMC04In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TRymm_YyZuSvY-fbHv8xxEVGoPu3eMlOVHO0AHZHsSnkCy_6FlUXDZIxM1e-WA4q5lfoUXozMq1MUubHe-ZfNNtJIMrEMeqXafCIvv2HKS8c4veppPj_YBmESEEI3L6z78DCFsb-8RXOpMePeiR8P3lIPyKMmHo3Qh_3AYhTd-AdXLbCjddTmGOBexx1hcBgeun-W8gqq7wIWgeekAhDtdoi8gS3FEPaBe8-b-EOdOocIz7Q35vXP_OMDkP93yVyEXXheoHkgjPWve4JIBhye9HhOBVn5m7NqZRkAtLY8-4tWb29TRfCURaHd7yOjzgIC6y5tWHAU6dJluilYb9djg';
    }
    
    private function makeRequest($method, $endpoint, $data = null)
    {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->baseUrl . $endpoint,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Accept: application/json',
                'Authorization: Bearer ' . $this->token
            ],
            CURLOPT_POSTFIELDS => $data ? json_encode($data) : null,
            CURLOPT_TIMEOUT => 30
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return [
            'status_code' => $httpCode,
            'body' => json_decode($response, true),
            'raw_response' => $response
        ];
    }
    
    public function testOriginalOrderFlow()
    {
        echo "=== Testing Original Order Flow (Online Payment) ===\n";
        
        $orderData = [
            'customer_id' => 3787,
            'customer_address' => 'Test Address, Mumbai',
            'location_code' => 1,
            'location_name' => 'Default Location',
            'city' => 1,
            'city_name' => 'Mumbai',
            'company_id' => 8163,
            'unit_id' => 8163,
            'fk_kitchen_code' => 1,
            'payment_method' => 'online',
            'total_amount' => 150.00,
            'subscription_days' => 5,
            'meals' => [
                [
                    'meal_type' => 'breakfast',
                    'product_code' => 339,
                    'quantity' => 1,
                    'unit_price' => 75.00
                ]
            ],
            'selected_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
        ];
        
        $response = $this->makeRequest('POST', '/create', $orderData);
        
        echo "Status Code: " . $response['status_code'] . "\n";
        echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n";
        
        if ($response['status_code'] === 201 && $response['body']['success']) {
            echo "✅ Original order flow working correctly\n";
            return $response['body']['data']['order_no'];
        } else {
            echo "❌ Original order flow failed\n";
            return null;
        }
    }
    
    public function testFullWalletPayment()
    {
        echo "\n=== Testing Full Wallet Payment ===\n";
        
        $orderData = [
            'customer_id' => 3787,
            'customer_address' => 'Test Address, Mumbai',
            'location_code' => 1,
            'location_name' => 'Default Location',
            'city' => 1,
            'city_name' => 'Mumbai',
            'company_id' => 8163,
            'unit_id' => 8163,
            'fk_kitchen_code' => 1,
            'payment_method' => 'wallet',
            'payment_method_final' => 'wallet',
            'total_amount' => 75.00,
            'wallet_amount_used' => 75.00,
            'gateway_amount' => 0,
            'subscription_days' => 1,
            'meals' => [
                [
                    'meal_type' => 'breakfast',
                    'product_code' => 339,
                    'quantity' => 1,
                    'unit_price' => 75.00
                ]
            ],
            'selected_days' => ['monday']
        ];
        
        $response = $this->makeRequest('POST', '/create', $orderData);
        
        echo "Status Code: " . $response['status_code'] . "\n";
        echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n";
        
        if ($response['status_code'] === 201 && $response['body']['success']) {
            echo "✅ Full wallet payment working correctly\n";
            return $response['body']['data']['order_no'];
        } else {
            echo "❌ Full wallet payment failed\n";
            return null;
        }
    }
    
    public function testPartialWalletPayment()
    {
        echo "\n=== Testing Partial Wallet Payment ===\n";
        
        $orderData = [
            'customer_id' => 3787,
            'customer_address' => 'Test Address, Mumbai',
            'location_code' => 1,
            'location_name' => 'Default Location',
            'city' => 1,
            'city_name' => 'Mumbai',
            'company_id' => 8163,
            'unit_id' => 8163,
            'fk_kitchen_code' => 1,
            'payment_method' => 'partial',
            'payment_method_final' => 'online',
            'total_amount' => 150.00,
            'wallet_amount_used' => 50.00,
            'gateway_amount' => 100.00,
            'subscription_days' => 2,
            'meals' => [
                [
                    'meal_type' => 'breakfast',
                    'product_code' => 339,
                    'quantity' => 1,
                    'unit_price' => 75.00
                ]
            ],
            'selected_days' => ['monday', 'tuesday']
        ];
        
        $response = $this->makeRequest('POST', '/create', $orderData);
        
        echo "Status Code: " . $response['status_code'] . "\n";
        echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n";
        
        if ($response['status_code'] === 201 && $response['body']['success']) {
            echo "✅ Partial wallet payment working correctly\n";
            return $response['body']['data']['order_no'];
        } else {
            echo "❌ Partial wallet payment failed\n";
            return null;
        }
    }
    
    public function testInsufficientWalletBalance()
    {
        echo "\n=== Testing Insufficient Wallet Balance Handling ===\n";
        
        $orderData = [
            'customer_id' => 3787,
            'customer_address' => 'Test Address, Mumbai',
            'location_code' => 1,
            'location_name' => 'Default Location',
            'city' => 1,
            'city_name' => 'Mumbai',
            'company_id' => 8163,
            'unit_id' => 8163,
            'fk_kitchen_code' => 1,
            'payment_method' => 'wallet',
            'payment_method_final' => 'wallet',
            'total_amount' => 10000.00, // Very high amount to test insufficient balance
            'wallet_amount_used' => 10000.00,
            'gateway_amount' => 0,
            'subscription_days' => 1,
            'meals' => [
                [
                    'meal_type' => 'breakfast',
                    'product_code' => 339,
                    'quantity' => 1,
                    'unit_price' => 10000.00
                ]
            ],
            'selected_days' => ['monday']
        ];
        
        $response = $this->makeRequest('POST', '/create', $orderData);
        
        echo "Status Code: " . $response['status_code'] . "\n";
        echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n";
        
        if ($response['status_code'] === 500 && !$response['body']['success']) {
            echo "✅ Insufficient wallet balance handled correctly\n";
            return true;
        } else {
            echo "❌ Insufficient wallet balance not handled properly\n";
            return false;
        }
    }
    
    public function runAllTests()
    {
        echo "Starting Order Wallet Flow Tests...\n\n";
        
        $results = [];
        
        // Test 1: Original order flow
        $results['original_flow'] = $this->testOriginalOrderFlow();
        
        // Test 2: Full wallet payment
        $results['full_wallet'] = $this->testFullWalletPayment();
        
        // Test 3: Partial wallet payment
        $results['partial_wallet'] = $this->testPartialWalletPayment();
        
        // Test 4: Insufficient wallet balance
        $results['insufficient_balance'] = $this->testInsufficientWalletBalance();
        
        echo "\n=== Test Results Summary ===\n";
        foreach ($results as $test => $result) {
            $status = $result ? "✅ PASSED" : "❌ FAILED";
            echo "{$test}: {$status}\n";
        }
        
        return $results;
    }
}

// Run the tests
$tester = new OrderWalletFlowTest();
$tester->runAllTests();
