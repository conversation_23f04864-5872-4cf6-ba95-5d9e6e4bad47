<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use App\Models\Holiday;
use App\Models\Plan;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Image;

/**
 * Settings Controller for Admin Service
 *
 * Handles application settings including dropdown values for forms
 */
class SettingsController extends Controller
{
    /**
     * Get dropdown settings for the application
     *
     * Returns dropdown values for Class, Division, Floor, and delivery days
     * extracted from existing customer address data or static values for company_id 8163
     */
    public function getDropdownSettings(Request $request): JsonResponse
    {
        try {
            // Validate company_id from query params (required, integer, max 99999)
            $validated = $request->validate([
                'company_id' => 'required|integer|max:99999',
            ]);
            $companyId = (int) $validated['company_id'];

            // Initialize variables
            $classes = [];
            $divisions = [];
            $floors = [];
            $allergies = [];

            // Include static lists only for company_id 8163
            if ($companyId === 8163) {
                $classes = $this->getStaticClasses();
                $divisions = $this->getStaticDivisions();
                $floors = $this->getStaticFloors();
                $allergies = $this->getStaticAllergies();
            }

            // Get delivery days from database (always dynamic based on weekoffs) using provided company_id
            $deliveryDays = $this->getDeliveryDays($companyId);

            return response()->json([
                'success' => true,
                'message' => 'Settings retrieved successfully',
                'data' => [
                    // Only include these when company_id is 8163; otherwise they will be empty arrays
                    'classes' => $classes,
                    'divisions' => $divisions,
                    'floors' => $floors,
                    'delivery_days' => $deliveryDays,
                    'allergies' => $allergies
                ],
                'meta' => [
                    'total_classes' => count($classes),
                    'total_divisions' => count($divisions),
                    'total_floors' => count($floors),
                    'total_delivery_days' => count($deliveryDays),
                    'total_allergies' => count($allergies),
                    'company_id' => $companyId,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching dropdown settings: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve settings',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get delivery days (weekdays) excluding weekoffs
     *
     * @return array
     */
    private function getDeliveryDays(int $companyId): array
    {
        try {
            // Get weekoff settings from database
            $weekoffSettings = $this->getWeekoffSettings($companyId);
            
            // Add logging for debugging
            Log::info('Weekoff settings found:', $weekoffSettings);
            
            // Parse weekoffs and determine delivery days
            $deliveryDays = $this->calculateDeliveryDays($weekoffSettings);
            
            // Add logging for debugging
            Log::info('Calculated delivery days:', array_column($deliveryDays, 'name'));
            
            return $deliveryDays;
        } catch (\Exception $e) {
            Log::error('Error fetching delivery days: ' . $e->getMessage());
            // Return static delivery days as fallback
            return $this->getStaticDeliveryDays();
        }
    }

    /**
     * Get weekoff settings from database for specific company
     *
     * @param int $companyId
     * @return array
     */
    private function getWeekoffSettings(int $companyId): array
    {
        $weekoffSettings = [];
        
        // Search for weekoff settings with different patterns
        $patterns = [
            '%WEEKOFF%',
            '%WEEK_OFF%', 
            '%HOLIDAY_DESCRIPTION%',
            '%WEEKLY_OFF%'
        ];
        
        foreach ($patterns as $pattern) {
            $settings = Setting::forCompany($companyId)
                ->where('key', 'LIKE', $pattern)
                ->get();
                
            foreach ($settings as $setting) {
                $weekoffSettings[$setting->key] = $setting->typed_value;
            }
        }
        
        return $weekoffSettings;
    }

    /**
     * Calculate delivery days based on weekoff settings
     * Sunday = 0, Monday = 1, ..., Saturday = 6
     *
     * @param array $weekoffSettings
     * @return array
     */
    private function calculateDeliveryDays(array $weekoffSettings): array
    {
        // Default all days are delivery days
        $allDays = [
            0 => ['id' => 0, 'name' => 'Sunday', 'short_name' => 'Sun', 'value' => 'sunday'],
            1 => ['id' => 1, 'name' => 'Monday', 'short_name' => 'Mon', 'value' => 'monday'],
            2 => ['id' => 2, 'name' => 'Tuesday', 'short_name' => 'Tue', 'value' => 'tuesday'],
            3 => ['id' => 3, 'name' => 'Wednesday', 'short_name' => 'Wed', 'value' => 'wednesday'],
            4 => ['id' => 4, 'name' => 'Thursday', 'short_name' => 'Thu', 'value' => 'thursday'],
            5 => ['id' => 5, 'name' => 'Friday', 'short_name' => 'Fri', 'value' => 'friday'],
            6 => ['id' => 6, 'name' => 'Saturday', 'short_name' => 'Sat', 'value' => 'saturday']
        ];
        
        $weekoffs = [];
        
        // Parse weekoff settings to extract weekoff days
        foreach ($weekoffSettings as $key => $value) {
            if ($value === null || $value === '') {
                continue;
            }
            
            Log::info("Processing weekoff setting: {$key} = {$value}");
            
            // Handle different formats of weekoff values
            $weekoffDays = $this->parseWeekoffValue($value);
            Log::info("Parsed weekoff days for {$key}:", $weekoffDays);
            
            $weekoffs = array_merge($weekoffs, $weekoffDays);
        }
        
        // Remove duplicates
        $weekoffs = array_unique($weekoffs);
        Log::info('Final weekoffs after merging:', $weekoffs);
        
        // Remove weekoff days from delivery days
        $deliveryDays = [];
        foreach ($allDays as $dayNumber => $dayInfo) {
            $isWeekoff = in_array($dayNumber, $weekoffs);
            Log::info("Day {$dayNumber} ({$dayInfo['name']}) - Is weekoff: " . ($isWeekoff ? 'YES' : 'NO'));
            
            if (!$isWeekoff) {
                $deliveryDays[] = $dayInfo;
            }
        }
        
        Log::info('Final delivery days count:', [count($deliveryDays)]);
        
        // If no delivery days found, return Monday to Friday as default
        if (empty($deliveryDays)) {
            Log::info('No delivery days found, returning static delivery days');
            return $this->getStaticDeliveryDays();
        }
        
        return $deliveryDays;
    }

    /**
     * Parse weekoff value to extract day numbers
     * Supports formats like: "6,0", "0,6", "6", "0", etc.
     *
     * @param string $value
     * @return array
     */
    private function parseWeekoffValue(string $value): array
    {
        $weekoffDays = [];
        
        Log::info("parseWeekoffValue called with: '{$value}'");
        
        // Split by comma first, then clean each part
        $parts = explode(',', $value);
        Log::info("Split into parts:", $parts);
        
        foreach ($parts as $part) {
            // Extract only digits
            $cleanPart = preg_replace('/[^0-9]/', '', trim($part));
            Log::info("Part: '{$part}' -> Clean: '{$cleanPart}'");
            
            if ($cleanPart !== '' && is_numeric($cleanPart)) {
                $day = (int) $cleanPart;
                Log::info("Day parsed: {$day}");
                if ($day >= 0 && $day <= 6) {
                    $weekoffDays[] = $day;
                    Log::info("Day {$day} added to weekoffs");
                }
            }
        }
        
        $result = array_unique($weekoffDays);
        Log::info("parseWeekoffValue returning:", $result);
        return $result;
    }

    /**
     * Get static delivery days (weekdays)
     *
     * @return array
     */
    private function getStaticDeliveryDays(): array
    {
        return [
            [
                'id' => 1,
                'name' => 'Monday',
                'short_name' => 'Mon',
                'value' => 'monday'
            ],
            [
                'id' => 2,
                'name' => 'Tuesday',
                'short_name' => 'Tue',
                'value' => 'tuesday'
            ],
            [
                'id' => 3,
                'name' => 'Wednesday',
                'short_name' => 'Wed',
                'value' => 'wednesday'
            ],
            [
                'id' => 4,
                'name' => 'Thursday',
                'short_name' => 'Thu',
                'value' => 'thursday'
            ],
            [
                'id' => 5,
                'name' => 'Friday',
                'short_name' => 'Fri',
                'value' => 'friday'
            ]
        ];
    }

    /**
     * Get default classes if database query fails
     *
     * @return array
     */
    private function getDefaultClasses(): array
    {
        return [
            'Nursery',
            'LKG',
            'UKG',
            'Class 1',
            'Class 2',
            'Class 3',
            'Class 4',
            'Class 5',
            'Class 6',
            'Class 7',
            'Class 8',
            'Class 9',
            'Class 10',
            'Class 11',
            'Class 12'
        ];
    }

    /**
     * Get default divisions if database query fails
     *
     * @return array
     */
    private function getDefaultDivisions(): array
    {
        return [
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'
        ];
    }

    /**
     * Get default floors if database query fails
     *
     * @return array
     */
    private function getDefaultFloors(): array
    {
        return [
            'Ground Floor',
            '1st Floor',
            '2nd Floor',
            '3rd Floor',
            '4th Floor',
            '5th Floor'
        ];
    }

    /**
     * Get static classes for company_id 8163
     *
     * @return array
     */
    private function getStaticClasses(): array
    {
        return [
            ['label' => 'Daycare', 'value' => 'Daycare'],
            ['label' => 'Nursery', 'value' => 'Nursery'],
            ['label' => 'JR KG', 'value' => 'JR KG'],
            ['label' => 'SR KG', 'value' => 'SR KG'],
            ['label' => '1', 'value' => '1'],
            ['label' => '2', 'value' => '2'],
            ['label' => '3', 'value' => '3'],
            ['label' => '4', 'value' => '4'],
            ['label' => '5', 'value' => '5'],
            ['label' => '6', 'value' => '6'],
            ['label' => '7', 'value' => '7'],
            ['label' => '8', 'value' => '8'],
            ['label' => '9', 'value' => '9'],
            ['label' => '10', 'value' => '10'],
            ['label' => '11', 'value' => '11'],
            ['label' => '12', 'value' => '12'],
            ['label' => 'Teacher', 'value' => 'Teacher']
        ];
    }

    /**
     * Get static divisions for company_id 8163
     *
     * @return array
     */
    private function getStaticDivisions(): array
    {
        return [
            ['label' => 'A', 'value' => 'A'],
            ['label' => 'B', 'value' => 'B'],
            ['label' => 'C', 'value' => 'C'],
            ['label' => 'D', 'value' => 'D'],
            ['label' => 'E', 'value' => 'E'],
            ['label' => 'F', 'value' => 'F'],
            ['label' => 'G', 'value' => 'G'],
            ['label' => 'H', 'value' => 'H'],
            ['label' => 'I', 'value' => 'I'],
            ['label' => 'J', 'value' => 'J'],
            ['label' => 'Science', 'value' => 'Science'],
            ['label' => 'Art', 'value' => 'Art'],
            ['label' => 'Commerce', 'value' => 'Commerce']
        ];
    }

    /**
     * Get static floors for company_id 8163
     *
     * @return array
     */
    private function getStaticFloors(): array
    {
        return [
            ['label' => 'Ground Floor', 'value' => 'Ground Floor'],
            ['label' => '1st Floor', 'value' => '1st Floor'],
            ['label' => '2nd Floor', 'value' => '2nd Floor'],
            ['label' => '3rd Floor', 'value' => '3rd Floor'],
            ['label' => '4th Floor', 'value' => '4th Floor'],
            ['label' => '5th Floor', 'value' => '5th Floor'],
            ['label' => '6th Floor', 'value' => '6th Floor'],
            ['label' => '7th Floor', 'value' => '7th Floor'],
            ['label' => '8th Floor', 'value' => '8th Floor'],
            ['label' => '9th Floor', 'value' => '9th Floor']
        ];
    }
    
    private function getStaticAllergies(): array {
        return [
            ['label' => 'Wheat', 'value' => 'Wheat'],
            ['label' => 'Lactose', 'value' => 'Lactose'],
            ['label' => 'Peanut', 'value' => 'Peanut'],
            ['label' => 'Chickpea', 'value' => 'Chickpea'],
            ['label' => 'Soy', 'value' => 'Soy']
        ];
    }/**
     * Get working days of a month
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getWorkingDays(Request $request): JsonResponse
    {
        try {
            // Validate required parameters
            $companyId = $request->input('company_id');
            $monthSelected = $request->input('month_selected', date('Y-m'));
            $requestedDays = $request->query('days', []); // Use query() to get all repeated params
            $kitchenId = $request->input('kitchen_id', 1); // Default to kitchen 1
            $mealType = $request->input('meal_type', 'lunch'); // Default to lunch

            if (!$companyId) {
                return response()->json([
                    'success' => false,
                    'message' => 'company_id is required'
                ], 400);
            }

            // Validate month format (YYYY-MM)
            if (!preg_match('/^\d{4}-\d{2}$/', $monthSelected)) {
                return response()->json([
                    'success' => false,
                    'message' => 'month_selected must be in format YYYY-MM (e.g., 2025-01)'
                ], 400);
            }

            // Validate meal type
            if (!in_array($mealType, ['breakfast', 'lunch'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'meal_type must be either breakfast or lunch'
                ], 400);
            }

            // --- Normalize requestedDays to always be an array ---
            if (is_string($requestedDays)) {
                // Handle comma-separated string (e.g., "1,2")
                if (strpos($requestedDays, ',') !== false) {
                    $requestedDays = explode(',', $requestedDays);
                } else {
                    $requestedDays = [$requestedDays];
                }
            }
            if (!is_array($requestedDays)) {
                $requestedDays = [$requestedDays];
            }

            // Get working days for the month with cut-off logic
            $workingDays = $this->calculateWorkingDaysForMonth($companyId, $monthSelected, $requestedDays, $kitchenId, $mealType);

            return response()->json([
                'success' => true,
                'message' => 'Working days retrieved successfully',
                'data' => [
                    'working_days' => $workingDays,
                    'month_selected' => $monthSelected,
                    'company_id' => $companyId,
                    'kitchen_id' => $kitchenId,
                    'meal_type' => $mealType,
                    'total_working_days' => count($workingDays)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching working days: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve working days',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Calculate working days for a specific month
     *
     * @param int $companyId
     * @param string $monthSelected Format: YYYY-MM
     * @param array $requestedDays Optional array of day numbers (0-6)
     * @param int $kitchenId Kitchen identifier for cut-off settings
     * @param string $mealType Meal type (breakfast or lunch)
     * @return array
     */
    private function calculateWorkingDaysForMonth(int $companyId, string $monthSelected, array $requestedDays = [], int $kitchenId = 1, string $mealType = 'lunch'): array
    {
        // Parse month and year
        [$year, $month] = explode('-', $monthSelected);
        $year = (int) $year;
        $month = (int) $month;

        // Get the first and last day of the month
        $firstDay = \Carbon\Carbon::createFromDate($year, $month, 1)->startOfMonth();
        $lastDay = \Carbon\Carbon::createFromDate($year, $month, 1)->endOfMonth();

        // Determine which days to consider as base working days
        if (empty($requestedDays)) {
            // Use existing delivery days logic
            $workingDayNumbers = $this->getWorkingDayNumbers($companyId);
        } else {
            // Validate requested days are in range 0-6
            $validDays = array_filter($requestedDays, function($day) {
                return is_numeric($day) && $day >= 0 && $day <= 6;
            });
            // Apply weekoff logic to requested days
            $weekoffSettings = $this->getWeekoffSettings($companyId);
            $weekoffs = $this->extractWeekoffDays($weekoffSettings);
            // Remove weekoff days from requested days
            $workingDayNumbers = array_diff($validDays, $weekoffs);
        }

        // Get holidays for the month
        $holidays = $this->getHolidaysForMonth($companyId, $firstDay->format('Y-m-d'), $lastDay->format('Y-m-d'));
        $holidayDates = $holidays->map(function($holiday) {
            return $holiday->holiday_date->format('j'); // Get day number (1-31)
        })->toArray();

        // Get cut-off settings for the kitchen and meal type
        $cutOffSettings = $this->getCutOffSettings($companyId, $kitchenId, $mealType);
        
        // Generate all dates in the month that match working day numbers
        $workingDays = [];
        $currentDate = $firstDay->copy();
        $today = \Carbon\Carbon::now();

        while ($currentDate <= $lastDay) {
            $dayOfWeek = $currentDate->dayOfWeek; // 0=Sunday, 1=Monday, ..., 6=Saturday
            $dayOfMonth = $currentDate->day;

            // Check if this day of week is a working day and not a holiday
            if (in_array($dayOfWeek, $workingDayNumbers) && !in_array($dayOfMonth, $holidayDates)) {
                // Apply cut-off logic to filter out unavailable dates
                if ($this->isDateAvailableForOrdering($currentDate, $today, $cutOffSettings)) {
                    $workingDays[] = $dayOfMonth;
                }
            }

            $currentDate->addDay();
        }

        return $workingDays;
    }

    /**
     * Get working day numbers from existing delivery days logic
     *
     * @param int $companyId
     * @return array
     */
    private function getWorkingDayNumbers(int $companyId): array
    {
        $weekoffSettings = $this->getWeekoffSettings($companyId);
        $deliveryDays = $this->calculateDeliveryDays($weekoffSettings);
        
        return array_map(function($day) {
            return $day['id'];
        }, $deliveryDays);
    }

    /**
     * Extract weekoff day numbers from settings
     *
     * @param array $weekoffSettings
     * @return array
     */
    private function extractWeekoffDays(array $weekoffSettings): array
    {
        $weekoffs = [];
        
        foreach ($weekoffSettings as $key => $value) {
            if ($value === null || $value === '') {
                continue;
            }
            
            $weekoffDays = $this->parseWeekoffValue($value);
            $weekoffs = array_merge($weekoffs, $weekoffDays);
        }
        
        return array_unique($weekoffs);
    }

    /**
     * Get holidays for a specific month
     *
     * @param int $companyId
     * @param string $startDate
     * @param string $endDate
     * @return \Illuminate\Support\Collection
     */
    private function getHolidaysForMonth(int $companyId, string $startDate, string $endDate)
    {
        return Holiday::forCompany($companyId)
            ->dateRange($startDate, $endDate)
            ->ofType('holiday')
            ->get();
    }

    /**
     * Calculate end date based on plan and working days
     *
     * Returns the end date for a meal plan based on the selected plan's quantity
     * and working days, excluding weekoffs and holidays
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function calculateEndDate(Request $request): JsonResponse
    {
        try {
            // Validate required parameters
            $companyId = $request->input('company_id');
            $startDate = $request->input('start_date');
            $planId = $request->input('plan_id');
            $unitId = $request->input('unit_id', 1);

            // Accept days param for end date calculation from both query and body
            $requestedDays = $request->query('days', null);
            if ($requestedDays === null) {
                $requestedDays = $request->input('days', []);
            }

            if (!$companyId || !$startDate || !$planId) {
                return response()->json([
                    'success' => false,
                    'message' => 'company_id, start_date, and plan_id are required'
                ], 400);
            }

            // Validate start date format
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $startDate)) {
                return response()->json([
                    'success' => false,
                    'message' => 'start_date must be in format YYYY-MM-DD (e.g., 2025-01-15)'
                ], 400);
            }

            // --- Normalize requestedDays to always be an array ---
            if (is_string($requestedDays)) {
                if (strpos($requestedDays, ',') !== false) {
                    $requestedDays = explode(',', $requestedDays);
                } else {
                    $requestedDays = [$requestedDays];
                }
            }
            if (!is_array($requestedDays)) {
                $requestedDays = [$requestedDays];
            }

            // Get plan details
            $plan = Plan::forCompany($companyId)
                ->where('pk_plan_code', $planId)
                ->active()
                ->first();

            if (!$plan) {
                return response()->json([
                    'success' => false,
                    'message' => 'Plan not found or inactive'
                ], 404);
            }

            // Calculate end date (pass requestedDays)
            $endDate = $this->calculatePlanEndDate($companyId, $startDate, $plan, $requestedDays);

            return response()->json([
                'success' => true,
                'message' => 'End date calculated successfully',
                'data' => [
                    'plan_id' => $plan->pk_plan_code,
                    'plan_name' => $plan->plan_name,
                    'plan_quantity' => $plan->plan_quantity,
                    'plan_period' => $plan->plan_period,
                    'plan_type' => $plan->plan_type,
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'total_days' => $this->calculateDateDifference($startDate, $endDate),
                    'company_id' => $companyId
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error calculating end date: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to calculate end date',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Calculate end date based on plan quantity and working days
     *
     * @param int $companyId
     * @param string $startDate Format: YYYY-MM-DD
     * @param Plan $plan
     * @param array $requestedDays Optional array of day numbers (0-6)
     * @return string End date in YYYY-MM-DD format
     */
    private function calculatePlanEndDate(int $companyId, string $startDate, Plan $plan, array $requestedDays = []): string
    {
        $startDateObj = \Carbon\Carbon::createFromFormat('Y-m-d', $startDate);
        $planQuantity = $plan->plan_quantity;
        Log::info("Calculating end date for plan", [
            'plan_id' => $plan->pk_plan_code,
            'plan_name' => $plan->plan_name,
            'plan_quantity' => $planQuantity,
            'start_date' => $startDate
        ]);

        if ($planQuantity <= 1) {
            Log::info("Single day plan, returning start date as end date");
            return $startDate;
        }

        $currentDate = $startDateObj->copy();
        $workingDaysFound = 0;
        $endDate = $startDate;

        // Get weekoff settings for the company
        $weekoffSettings = $this->getWeekoffSettings($companyId);
        $weekoffDays = $this->extractWeekoffDays($weekoffSettings);
        Log::info("Weekoff days for company {$companyId}:", $weekoffDays);

        // If requestedDays is provided, use it (after removing weekoffs)
        if (!empty($requestedDays)) {
            $validDays = array_filter($requestedDays, function($day) {
                return is_numeric($day) && $day >= 0 && $day <= 6;
            });
            $workingDayNumbers = array_diff($validDays, $weekoffDays);
        } else {
            // Use all non-weekoff days
            $workingDayNumbers = array_diff([0,1,2,3,4,5,6], $weekoffDays);
        }

        // Find planQuantity working days starting from startDate
        while ($workingDaysFound < $planQuantity) {
            $dayOfWeek = $currentDate->dayOfWeek;
            $currentDateStr = $currentDate->format('Y-m-d');

            // Get holidays for the current month (cache monthly holidays)
            $currentMonth = $currentDate->format('Y-m');
            static $cachedHolidays = [];
            if (!isset($cachedHolidays[$currentMonth])) {
                $monthStart = $currentDate->copy()->startOfMonth()->format('Y-m-d');
                $monthEnd = $currentDate->copy()->endOfMonth()->format('Y-m-d');
                $holidays = $this->getHolidaysForMonth($companyId, $monthStart, $monthEnd);
                $cachedHolidays[$currentMonth] = $holidays->map(function($holiday) {
                    return $holiday->holiday_date->format('Y-m-d');
                })->toArray();
            }
            $holidayDates = $cachedHolidays[$currentMonth];
            $isWeekoff = in_array($dayOfWeek, $weekoffDays);
            $isHoliday = in_array($currentDateStr, $holidayDates);

            if (in_array($dayOfWeek, $workingDayNumbers) && !$isHoliday) {
                $workingDaysFound++;
                $endDate = $currentDateStr;
            }
            $currentDate = $currentDate->addDay();
            if ($currentDate->diffInDays($startDateObj) > 365) {
                Log::warning("Breaking loop after 365 days to prevent infinite loop");
                break;
            }
        }
        Log::info("Final end date calculated", [
            'end_date' => $endDate,
            'total_working_days_found' => $workingDaysFound,
            'plan_quantity_required' => $planQuantity
        ]);
        return $endDate;
    }

    /**
     * Calculate the difference in days between two dates
     *
     * @param string $startDate
     * @param string $endDate
     * @return int
     */
    private function calculateDateDifference(string $startDate, string $endDate): int
    {
        $start = \Carbon\Carbon::createFromFormat('Y-m-d', $startDate);
        $end = \Carbon\Carbon::createFromFormat('Y-m-d', $endDate);
        
        return $start->diffInDays($end) + 1; // +1 to include both start and end dates
    }

    /**
     * Get cut-off settings for a specific kitchen and meal type
     *
     * @param int $companyId
     * @param int $kitchenId
     * @param string $mealType (breakfast or lunch)
     * @return array
     */
    private function getCutOffSettings(int $companyId, int $kitchenId, string $mealType): array
    {
        $mealTypeUpper = strtoupper($mealType);
        
        // Build the setting keys based on kitchen ID and meal type
        $cutOffDayKey = "K{$kitchenId}_{$mealTypeUpper}_ORDER_CUT_OFF_DAY";
        $cutOffTimeKey = "K{$kitchenId}_{$mealTypeUpper}_ORDER_CUT_OFF_TIME";
        
        Log::info("Looking for cut-off settings", [
            'cut_off_day_key' => $cutOffDayKey,
            'cut_off_time_key' => $cutOffTimeKey
        ]);
        
        // Get the settings from database
        $cutOffDaySetting = Setting::forCompany($companyId)->where('key', $cutOffDayKey)->first();
        $cutOffTimeSetting = Setting::forCompany($companyId)->where('key', $cutOffTimeKey)->first();
        
        $cutOffDay = $cutOffDaySetting ? $cutOffDaySetting->typed_value : 0; // Default to same day
        $cutOffTime = $cutOffTimeSetting ? $cutOffTimeSetting->typed_value : '23:59:59'; // Default to end of day
        
        Log::info("Cut-off settings retrieved", [
            'cut_off_day' => $cutOffDay,
            'cut_off_time' => $cutOffTime
        ]);
        
        return [
            'cut_off_day' => (int) $cutOffDay,
            'cut_off_time' => $cutOffTime
        ];
    }

    /**
     * Check if a date is available for ordering based on cut-off settings
     *
     * @param \Carbon\Carbon $targetDate The date to check
     * @param \Carbon\Carbon $currentDateTime Current date and time
     * @param array $cutOffSettings Cut-off settings array
     * @return bool
     */
    private function isDateAvailableForOrdering(\Carbon\Carbon $targetDate, \Carbon\Carbon $currentDateTime, array $cutOffSettings): bool
    {
        // Remove past dates (dates before today)
        $today = $currentDateTime->copy()->startOfDay();
        $targetDateOnly = $targetDate->copy()->startOfDay();
        
        if ($targetDateOnly->lt($today)) {
            Log::info("Date {$targetDate->format('Y-m-d')} is in the past, excluding");
            return false;
        }
        
        // For future dates (not today), they are available
        if ($targetDateOnly->gt($today)) {
            Log::info("Date {$targetDate->format('Y-m-d')} is in the future, including");
            return true;
        }
        
        // For today's date, check cut-off logic
        $cutOffDay = $cutOffSettings['cut_off_day'];
        $cutOffTime = $cutOffSettings['cut_off_time'];
        
        // Calculate the actual cut-off datetime
        $cutOffDateTime = $currentDateTime->copy()->startOfDay();
        
        // Add cut-off days
        if ($cutOffDay > 0) {
            $cutOffDateTime->addDays($cutOffDay);
        }
        
        // Parse and set cut-off time
        try {
            $timeParts = explode(':', $cutOffTime);
            if (count($timeParts) >= 2) {
                $hour = (int) $timeParts[0];
                $minute = (int) $timeParts[1];
                $second = isset($timeParts[2]) ? (int) $timeParts[2] : 0;
                
                $cutOffDateTime->setTime($hour, $minute, $second);
            }
        } catch (\Exception $e) {
            Log::warning("Failed to parse cut-off time: {$cutOffTime}", ['error' => $e->getMessage()]);
            $cutOffDateTime->setTime(23, 59, 59); // Default to end of day
        }
        
        // If cut-off day is 0 (same day), check if current time is before cut-off time
        if ($cutOffDay == 0) {
            $isAvailable = $currentDateTime->lt($cutOffDateTime);
            Log::info("Same day cut-off check", [
                'target_date' => $targetDate->format('Y-m-d'),
                'current_time' => $currentDateTime->format('Y-m-d H:i:s'),
                'cut_off_time' => $cutOffDateTime->format('Y-m-d H:i:s'),
                'is_available' => $isAvailable
            ]);
            return $isAvailable;
        }
        
        // For cut-off day > 0, today is available if we're before the cut-off time of the target day
        $isAvailable = $currentDateTime->lt($cutOffDateTime);
        Log::info("Next day cut-off check", [
            'target_date' => $targetDate->format('Y-m-d'),
            'current_time' => $currentDateTime->format('Y-m-d H:i:s'),
            'cut_off_datetime' => $cutOffDateTime->format('Y-m-d H:i:s'),
            'cut_off_day' => $cutOffDay,
            'is_available' => $isAvailable
        ]);
        
        return $isAvailable;
    }

    /**
     * Get enabled payment gateways and their configuration
     *
     * Returns gateways enabled by admin (from ONLINE_PAYMENT_GATEWAY) and their related keys (key, secret, salt, etc.)
     */
    public function getPaymentGateways(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            // Validate company_id from query params (required, integer, max 99999)
            $validated = $request->validate([
                'company_id' => 'required|integer|max:99999',
                'kitchen_id' => 'sometimes|integer|max:9',
            ]);
            // Get enabled gateways (comma-separated string or JSON array)
            $companyId = (int) $validated['company_id'];
            $kitchenId = (int) $validated['kitchen_id'];

            $enabledSetting = Setting::select('key', 'value')
            ->where('company_id', $companyId)
            ->whereIn('key', [
                'EXPRESS_EXTENDED_ENABLED',
                'APPLY_GATEWAY_TRANSACTION_CHARGES',
                'GLOBAL_CUSTOMER_PAYMENT_MODE',
                'K'.$kitchenId.'_CUSTOMER_PAYMENT_MODE',
                'GLOBAL_ALLOW_PARTIAL_PAYMENT',
                'ONLINE_PAYMENT_GATEWAY'
            ])->get();

            $enabled = [];
            // Create a settings lookup map for O(1) access
            $settingsMap = [];
            foreach ($enabledSetting as $setting) {
                if (is_array($setting)) {
                    $settingsMap[$setting['key']] = $setting['value'] ?? null;
                } elseif (is_object($setting)) {
                    $settingsMap[$setting->key] = $setting->value ?? null;
                }
            }

            // Define setting key mappings for cleaner processing
            $settingMappings = [
                'GLOBAL_ALLOW_PARTIAL_PAYMENT' => 'partial_payment_enabled',
                'EXPRESS_EXTENDED_ENABLED' => 'express_order_enabled',
                'APPLY_GATEWAY_TRANSACTION_CHARGES' => 'apply_gateway_transaction_charges',
                'ONLINE_PAYMENT_GATEWAY' => 'enabled_gateways'
            ];

            // Process boolean settings efficiently
            foreach ($settingMappings as $settingKey => $enabledKey) {
                $enabled[$enabledKey] = ($settingsMap[$settingKey] ?? 'no') === 'yes';
            }

            // Handle ONLINE_PAYMENT_GATEWAY (special case - array/JSON value)
            if (isset($settingsMap['ONLINE_PAYMENT_GATEWAY'])) {
                $val = $settingsMap['ONLINE_PAYMENT_GATEWAY'];
                if (is_string($val) && (str_starts_with($val, '[') || str_starts_with($val, '{'))) {
                    $enabled['online_payment_gateways'] = json_decode($val, true) ?: [];
                } else {
                    $enabled['online_payment_gateways'] = array_filter(array_map('trim', explode(',', $val)));
                }
            }

            // Handle wallet settings (requires both global and kitchen-specific settings)
            $globalWalletEnabled = ($settingsMap['GLOBAL_CUSTOMER_PAYMENT_MODE'] ?? 'no') === 'yes';
            $kitchenWalletEnabled = ($settingsMap['K'.$kitchenId.'_CUSTOMER_PAYMENT_MODE'] ?? 'no') === 'yes';
            $enabled['k'.$kitchenId.'wallet_enabled'] = $globalWalletEnabled && $kitchenWalletEnabled;

            // For each gateway, fetch related keys (e.g., RAZORPAY_KEY, RAZORPAY_SECRET, PAYU_KEY, PAYU_SALT, etc.)
            // $gateways = [];
            // foreach ($enabled as $gateway) {
            //     $prefix = strtoupper($gateway);
            //     $keys = Setting::where('key', 'like', 'GATEWAY_'. $prefix . '_%')->get();
            //     $config = [];
            //     foreach ($keys as $setting) {
            //         $keyName = strtolower(str_replace($prefix . '_', '', $setting->key));
            //         $config[$keyName] = $setting->value;
            //     }
            //     $gateways[$gateway] = $config;
            // }

            return response()->json([
                'success' => true,
                'message' => 'Payment gateways retrieved successfully',
                'data' => [
                    'enabled_gateways' => array_values($enabled),
                    // 'gateways' => $gateways
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching payment gateways: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment gateways',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    public function getAllImages(Request $request) {
        try {
            // Validate company_id from query params (required, integer, max 99999)
            $validated = $request->validate([
                'company_id' => 'required|integer|max:99999',
            ]);
            $companyId = (int) $validated['company_id'];

            $bannerImages = Image::select('image_path','image_title','company_id')
                ->where('cms_id',1)
                ->where('company_id', $companyId)
                ->orderBy('position')
                ->get();
            $weeklyPlanImage = Image::select('image_path','image_title','company_id')
                ->where('company_id', $companyId)
                ->where('image_path','like','%weekly%')
                ->first();

            if(!empty($weeklyPlanImage)){
                $weeklyPlanImage->image_path = $this->assetUrl($weeklyPlanImage->image_path, $weeklyPlanImage->company_id);
            }

            $bannerImages = $bannerImages->map(function ($image) {
                $image->image_path = $this->assetUrl($image->image_path, $image->company_id);
                return $image;
            });

            $images = [
                'banners' => $bannerImages,
                'weekly-planner' => $weeklyPlanImage
            ];

            return response()->json([
                'success' => true,
                'message' => 'Images retrieved successfully',
                'data' => $images
            ]);
            
        } catch (\Exception $e) {
            \Log::error('Error fetching images: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve images',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    private function assetUrl($imagePath, $companyId){
        if(empty($imagePath) || empty($companyId)){
            return null;
        }

        $region = config('filesystems.disks.s3.region');
        $bucket = config('filesystems.disks.s3.bucket');

        return 'https://s3.'.$region.'.amazonaws.com/'.$bucket.'/'.$companyId.'/cms/'.$imagePath;
    }
}
