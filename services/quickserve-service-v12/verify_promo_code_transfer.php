<?php

/**
 * Verification script to test that promo code data is correctly transferred
 * from temp_pre_orders to orders table after order creation
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🔍 Verifying Promo Code Transfer from temp_pre_orders to orders\n";
echo "=" . str_repeat("=", 60) . "\n\n";

try {
    // Test 1: Check if both tables have promo code columns
    echo "Test 1: Checking table structures...\n";
    
    $tempPreOrdersColumns = DB::select("SHOW COLUMNS FROM temp_pre_orders LIKE '%promo%'");
    $ordersColumns = DB::select("SHOW COLUMNS FROM orders LIKE '%promo%'");
    
    echo "temp_pre_orders promo columns:\n";
    foreach ($tempPreOrdersColumns as $column) {
        echo "  - {$column->Field} ({$column->Type})\n";
    }
    
    echo "orders promo columns:\n";
    foreach ($ordersColumns as $column) {
        echo "  - {$column->Field} ({$column->Type})\n";
    }
    
    if (count($tempPreOrdersColumns) >= 2 && count($ordersColumns) >= 2) {
        echo "✅ Both tables have promo_code and system_promo_code columns\n";
    } else {
        echo "❌ Missing promo code columns in one or both tables\n";
        exit(1);
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
    
    // Test 2: Find recent orders with promo codes
    echo "Test 2: Finding recent orders with promo codes...\n";
    
    $recentOrdersWithPromo = DB::table('orders')
        ->whereNotNull('promo_code')
        ->where('created_date', '>=', now()->subDays(7))
        ->orderBy('created_date', 'desc')
        ->limit(5)
        ->get(['order_no', 'promo_code', 'system_promo_code', 'applied_discount', 'ref_order', 'created_date']);
    
    if ($recentOrdersWithPromo->count() > 0) {
        echo "Found {$recentOrdersWithPromo->count()} recent orders with promo codes:\n";
        foreach ($recentOrdersWithPromo as $order) {
            echo "  Order: {$order->order_no}\n";
            echo "    Promo Code: " . ($order->promo_code ?? 'NULL') . "\n";
            echo "    System Promo Code: " . ($order->system_promo_code ?? 'NULL') . "\n";
            echo "    Applied Discount: ₹{$order->applied_discount}\n";
            echo "    Ref Order (temp): {$order->ref_order}\n";
            echo "    Created: {$order->created_date}\n\n";
        }
        echo "✅ Found orders with promo codes in orders table\n";
    } else {
        echo "ℹ️  No recent orders with promo codes found\n";
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
    
    // Test 3: Check temp_pre_orders with promo codes
    echo "Test 3: Finding recent temp_pre_orders with promo codes...\n";
    
    $recentTempOrdersWithPromo = DB::table('temp_pre_orders')
        ->whereNotNull('promo_code')
        ->where('last_modified', '>=', now()->subDays(7))
        ->orderBy('last_modified', 'desc')
        ->limit(5)
        ->get(['order_no', 'promo_code', 'system_promo_code', 'applied_discount', 'pk_order_no', 'last_modified']);
    
    if ($recentTempOrdersWithPromo->count() > 0) {
        echo "Found {$recentTempOrdersWithPromo->count()} recent temp orders with promo codes:\n";
        foreach ($recentTempOrdersWithPromo as $tempOrder) {
            echo "  Temp Order: {$tempOrder->order_no}\n";
            echo "    Promo Code: " . ($tempOrder->promo_code ?? 'NULL') . "\n";
            echo "    System Promo Code: " . ($tempOrder->system_promo_code ?? 'NULL') . "\n";
            echo "    Applied Discount: ₹{$tempOrder->applied_discount}\n";
            echo "    PK Order No: {$tempOrder->pk_order_no}\n";
            echo "    Last Modified: {$tempOrder->last_modified}\n\n";
        }
        echo "✅ Found temp orders with promo codes\n";
    } else {
        echo "ℹ️  No recent temp orders with promo codes found\n";
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
    
    // Test 4: Check if temp orders with promo codes have corresponding orders
    echo "Test 4: Checking if temp orders with promo codes have corresponding orders...\n";

    $tempOrdersWithCorrespondingOrders = DB::select("
        SELECT
            t.order_no as temp_order_no,
            t.promo_code as temp_promo_code,
            t.system_promo_code as temp_system_promo_code,
            t.applied_discount as temp_discount,
            t.pk_order_no,
            o.order_no as order_no,
            o.promo_code as order_promo_code,
            o.system_promo_code as order_system_promo_code,
            o.applied_discount as order_discount,
            o.ref_order
        FROM temp_pre_orders t
        LEFT JOIN orders o ON o.ref_order = t.pk_order_no
        WHERE t.promo_code IS NOT NULL
           OR t.system_promo_code IS NOT NULL
        ORDER BY t.last_modified DESC
        LIMIT 10
    ");

    if (count($tempOrdersWithCorrespondingOrders) > 0) {
        echo "Temp orders with promo codes and their corresponding orders:\n";
        $withOrdersCount = 0;
        $withoutOrdersCount = 0;

        foreach ($tempOrdersWithCorrespondingOrders as $check) {
            echo "  Temp Order: {$check->temp_order_no} (PK: {$check->pk_order_no})\n";
            echo "    Temp Promo: " . ($check->temp_promo_code ?? 'NULL') . "\n";
            echo "    Temp System: " . ($check->temp_system_promo_code ?? 'NULL') . "\n";

            if ($check->order_no) {
                echo "    ✅ Has Order: {$check->order_no}\n";
                echo "    Order Promo: " . ($check->order_promo_code ?? 'NULL') . "\n";
                echo "    Order System: " . ($check->order_system_promo_code ?? 'NULL') . "\n";

                $promoMatch = ($check->temp_promo_code == $check->order_promo_code);
                $systemMatch = ($check->temp_system_promo_code == $check->order_system_promo_code);

                if ($promoMatch && $systemMatch) {
                    echo "    ✅ Promo codes match!\n";
                } else {
                    echo "    ❌ Promo codes don't match!\n";
                }

                $withOrdersCount++;
            } else {
                echo "    ❌ No corresponding order found\n";
                $withoutOrdersCount++;
            }
            echo "\n";
        }

        echo "Summary: {$withOrdersCount} temp orders have corresponding orders, {$withoutOrdersCount} don't\n";

        if ($withoutOrdersCount === 0) {
            echo "✅ All temp orders with promo codes have corresponding orders!\n";
        } else {
            echo "⚠️  {$withoutOrdersCount} temp orders with promo codes don't have corresponding orders\n";
        }
    } else {
        echo "ℹ️  No temp orders with promo codes found\n";
    }

    echo "\n" . str_repeat("-", 50) . "\n\n";

    // Test 5: Verify data consistency between temp_pre_orders and orders
    echo "Test 5: Verifying promo code transfer consistency for existing orders...\n";

    $consistencyCheck = DB::select("
        SELECT
            t.order_no as temp_order_no,
            t.promo_code as temp_promo_code,
            t.system_promo_code as temp_system_promo_code,
            t.applied_discount as temp_discount,
            o.order_no as order_no,
            o.promo_code as order_promo_code,
            o.system_promo_code as order_system_promo_code,
            o.applied_discount as order_discount,
            CASE
                WHEN t.promo_code = o.promo_code AND t.system_promo_code = o.system_promo_code
                THEN 'MATCH'
                ELSE 'MISMATCH'
            END as promo_status
        FROM temp_pre_orders t
        INNER JOIN orders o ON o.ref_order = t.pk_order_no
        WHERE t.promo_code IS NOT NULL
           OR t.system_promo_code IS NOT NULL
           OR o.promo_code IS NOT NULL
           OR o.system_promo_code IS NOT NULL
        ORDER BY t.last_modified DESC
        LIMIT 10
    ");

    if (count($consistencyCheck) > 0) {
        echo "Promo code transfer consistency check:\n";
        $matchCount = 0;
        $mismatchCount = 0;

        foreach ($consistencyCheck as $check) {
            echo "  Order: {$check->order_no}\n";
            echo "    Temp Promo: " . ($check->temp_promo_code ?? 'NULL') . " -> Order Promo: " . ($check->order_promo_code ?? 'NULL') . "\n";
            echo "    Temp System: " . ($check->temp_system_promo_code ?? 'NULL') . " -> Order System: " . ($check->order_system_promo_code ?? 'NULL') . "\n";
            echo "    Status: {$check->promo_status}\n\n";

            if ($check->promo_status === 'MATCH') {
                $matchCount++;
            } else {
                $mismatchCount++;
            }
        }

        echo "Summary: {$matchCount} matches, {$mismatchCount} mismatches\n";

        if ($mismatchCount === 0) {
            echo "✅ All promo codes transferred correctly!\n";
        } else {
            echo "⚠️  Found {$mismatchCount} mismatches in promo code transfer\n";
        }
    } else {
        echo "ℹ️  No orders found with promo code data to verify\n";
    }
    
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "🎉 Verification completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error during verification: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
