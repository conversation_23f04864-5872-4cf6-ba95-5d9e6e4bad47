<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Api\V2\OrderManagementController;
use Illuminate\Http\Request;

class WalletAndPaymentFixesTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test database with required tables and data
        $this->setupTestData();
    }

    protected function setupTestData(): void
    {
        // Create settings table and add GATEWAY_TRANSACTION_CHARGES_AMOUNT = 0
        DB::statement('CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            `key` VARCHAR(255) NOT NULL,
            `value` TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )');

        DB::table('settings')->insert([
            'key' => 'GATEWAY_TRANSACTION_CHARGES_AMOUNT',
            'value' => '0'
        ]);

        // Create temp_pre_orders table
        DB::statement('CREATE TABLE IF NOT EXISTS temp_pre_orders (
            pk_order_no BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            company_id INT UNSIGNED DEFAULT 1,
            unit_id INT UNSIGNED DEFAULT 1,
            order_no VARCHAR(45),
            customer_code INT,
            customer_name VARCHAR(45),
            total_amt DECIMAL(12,2),
            amount DECIMAL(12,2),
            delivery_charges DECIMAL(12,2) DEFAULT 0,
            order_menu VARCHAR(45),
            order_days TEXT,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )');

        // Create payment_transaction table
        DB::statement('CREATE TABLE IF NOT EXISTS payment_transaction (
            pk_transaction_id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            company_id INT UNSIGNED DEFAULT 1,
            unit_id INT UNSIGNED DEFAULT 1,
            customer_id INT,
            customer_email VARCHAR(100),
            customer_phone VARCHAR(20),
            customer_name VARCHAR(100),
            payment_amount DECIMAL(10,2),
            transaction_charges DECIMAL(10,2) DEFAULT 0,
            wallet_amount DECIMAL(10,2) DEFAULT 0,
            pre_order_id VARCHAR(100),
            gateway VARCHAR(50),
            status VARCHAR(20) DEFAULT "initiated",
            gateway_transaction_id VARCHAR(200),
            description TEXT,
            transaction_by VARCHAR(50) DEFAULT "gateway",
            referer VARCHAR(50) DEFAULT "website",
            success_url TEXT,
            failure_url TEXT,
            context VARCHAR(50),
            discount DECIMAL(10,2) DEFAULT 0,
            recurring BOOLEAN DEFAULT FALSE,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )');

        // Create customer_wallet table
        DB::statement('CREATE TABLE IF NOT EXISTS customer_wallet (
            customer_wallet_id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            company_id INT UNSIGNED DEFAULT 1,
            unit_id INT UNSIGNED DEFAULT 1,
            fk_customer_code INT,
            wallet_amount DECIMAL(10,2),
            amount_type ENUM("cr", "dr", "lock"),
            description TEXT,
            payment_date DATE,
            payment_type VARCHAR(50),
            context VARCHAR(50),
            reference_no VARCHAR(100),
            created_by INT,
            updated_by INT,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )');

        // Create payment_transfered table
        DB::statement('CREATE TABLE IF NOT EXISTS payment_transfered (
            pk_transfer_id VARCHAR(50) PRIMARY KEY,
            company_id INT UNSIGNED DEFAULT 1,
            unit_id INT UNSIGNED DEFAULT 1,
            fk_transaction_id BIGINT UNSIGNED,
            source VARCHAR(200),
            recipient VARCHAR(200),
            amount DECIMAL(10,2),
            currency VARCHAR(3) DEFAULT "INR",
            amount_reversed DECIMAL(10,2) DEFAULT 0,
            transfered_at TIMESTAMP,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )');

        // Create orders table
        DB::statement('CREATE TABLE IF NOT EXISTS orders (
            pk_order_no BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            company_id INT UNSIGNED DEFAULT 1,
            unit_id INT UNSIGNED DEFAULT 1,
            order_no VARCHAR(45),
            ref_order BIGINT UNSIGNED,
            customer_code INT,
            order_date DATE,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )');
    }

    public function test_transaction_charges_respects_zero_setting()
    {
        // Test that transaction charges are 0 when GATEWAY_TRANSACTION_CHARGES_AMOUNT is 0
        $controller = $this->app->make(OrderManagementController::class);
        
        // Use reflection to test the protected method
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('calculateTransactionCharges');
        $method->setAccessible(true);

        $charges = $method->invoke($controller, 100.00);
        $this->assertEquals(0.00, $charges, 'Transaction charges should be 0 when setting is 0');

        $charges = $method->invoke($controller, 500.00);
        $this->assertEquals(0.00, $charges, 'Transaction charges should be 0 for any amount when setting is 0');
    }

    public function test_transaction_charges_calculates_correctly_with_non_zero_setting()
    {
        // Update setting to 3%
        DB::table('settings')
            ->where('key', 'GATEWAY_TRANSACTION_CHARGES_AMOUNT')
            ->update(['value' => '3']);

        $controller = $this->app->make(OrderManagementController::class);
        
        // Use reflection to test the protected method
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('calculateTransactionCharges');
        $method->setAccessible(true);

        $charges = $method->invoke($controller, 100.00);
        $this->assertEquals(3.00, $charges, 'Transaction charges should be 3% of amount');

        $charges = $method->invoke($controller, 500.00);
        $this->assertEquals(15.00, $charges, 'Transaction charges should be 3% of amount');
    }

    public function test_payment_context_is_set_correctly()
    {
        // Test different payment scenarios and their contexts
        $testCases = [
            ['wallet_amount' => 0, 'gateway_amount' => 100, 'expected_context' => 'order_payment'],
            ['wallet_amount' => 100, 'gateway_amount' => 0, 'expected_context' => 'wallet_payment'],
            ['wallet_amount' => 50, 'gateway_amount' => 50, 'expected_context' => 'partial_payment'],
        ];

        foreach ($testCases as $case) {
            $validated = [
                'company_id' => 1,
                'unit_id' => 1,
                'customer_id' => 1001,
                'customer_details' => [
                    'email' => '<EMAIL>',
                    'phone' => '1234567890',
                    'name' => 'Test Customer'
                ],
                'total_amount' => $case['wallet_amount'] + $case['gateway_amount'],
                'wallet_amount_used' => $case['wallet_amount'],
                'gateway_amount' => $case['gateway_amount'],
                'payment_method_final' => $case['gateway_amount'] > 0 ? 'online' : 'wallet'
            ];

            $orderNo = 'TEST' . time() . rand(1000, 9999);
            
            $controller = $this->app->make(OrderManagementController::class);
            $reflection = new \ReflectionClass($controller);
            $method = $reflection->getMethod('createInitialPaymentTransaction');
            $method->setAccessible(true);

            $transactionId = $method->invoke($controller, 1, $orderNo, $validated);

            // Verify the context was set correctly
            $transaction = DB::table('payment_transaction')
                ->where('pk_transaction_id', $transactionId)
                ->first();

            $this->assertEquals($case['expected_context'], $transaction->context, 
                "Context should be {$case['expected_context']} for wallet:{$case['wallet_amount']}, gateway:{$case['gateway_amount']}");
        }
    }

    public function test_wallet_entries_created_based_on_payment_transaction()
    {
        // Create test data
        $customerId = 1001;
        $orderNo = 'TEST' . time();
        
        // Insert test temp_pre_order
        DB::table('temp_pre_orders')->insert([
            'order_no' => $orderNo,
            'customer_code' => $customerId,
            'customer_name' => 'Test Customer',
            'total_amt' => 150.00,
            'amount' => 150.00,
            'delivery_charges' => 0,
            'order_menu' => 'breakfast'
        ]);

        // Insert test payment_transaction
        $transactionId = DB::table('payment_transaction')->insertGetId([
            'customer_id' => $customerId,
            'customer_email' => '<EMAIL>',
            'customer_phone' => '1234567890',
            'customer_name' => 'Test Customer',
            'payment_amount' => 150.00,
            'transaction_charges' => 0.00, // Should be 0 based on setting
            'wallet_amount' => 0.00,
            'pre_order_id' => $orderNo,
            'gateway' => 'razorpay',
            'status' => 'completed',
            'referer' => 'order_api',
            'context' => 'order_payment'
        ]);

        // Test wallet entry creation
        $controller = $this->app->make(OrderManagementController::class);
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('createWalletEntriesFromPaymentTransaction');
        $method->setAccessible(true);

        $paymentTransaction = DB::table('payment_transaction')->where('pk_transaction_id', $transactionId)->first();
        $tempOrders = collect([DB::table('temp_pre_orders')->where('order_no', $orderNo)->first()]);

        $method->invoke($controller, $customerId, 1, 1, $paymentTransaction, 'razorpay', $tempOrders);

        // Verify wallet entries
        $walletEntries = DB::table('customer_wallet')
            ->where('fk_customer_code', $customerId)
            ->get();

        // Should have 1 credit entry for payment_amount (150.00)
        // Should have 0 credit entries for transaction_charges (0.00)
        // Should have 1 debit entry for order amount (150.00)
        
        $creditEntries = $walletEntries->where('amount_type', 'cr');
        $debitEntries = $walletEntries->where('amount_type', 'dr');

        $this->assertEquals(1, $creditEntries->count(), 'Should have 1 credit entry for payment amount');
        $this->assertEquals(1, $debitEntries->count(), 'Should have 1 debit entry for order amount');
        
        $creditEntry = $creditEntries->first();
        $this->assertEquals(150.00, $creditEntry->wallet_amount, 'Credit entry should be for payment amount');
        $this->assertEquals($transactionId, $creditEntry->reference_no, 'Credit entry should reference transaction ID');
    }
}
