<?php

namespace Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Api\V2\OrderManagementController;
use Illuminate\Http\Request;

class PromoCodeTransferTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that promo codes are correctly transferred from temp_pre_orders to orders
     */
    public function test_promo_code_transfer_from_temp_to_orders()
    {
        // Create a mock temp_pre_order with promo code data
        $tempPreOrderId = DB::table('temp_pre_orders')->insertGetId([
            'company_id' => 8163,
            'unit_id' => 8163,
            'fk_kitchen_code' => 1,
            'ref_order' => 0,
            'order_no' => 'TEST' . time(),
            'auth_id' => 3800,
            'customer_code' => 3800,
            'customer_name' => 'Test Customer',
            'food_preference' => '[]',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
            'location_code' => 1,
            'location_name' => 'Test Location',
            'city' => 9,
            'city_name' => 'Test City',
            'product_code' => 342,
            'product_name' => 'Test Meal',
            'product_description' => 'Test meal description',
            'product_type' => 'Meal',
            'quantity' => 1,
            'order_type' => 'Day',
            'order_days' => '2025-09-09',
            'promo_code' => 'TESTPROMO', // User-entered promo code
            'system_promo_code' => 'SYSTEM10', // System-generated promo code
            'product_price' => 100.00,
            'amount' => 90.00, // After discount
            'total_amt' => 90.00,
            'tax' => 9.00,
            'total_tax' => 9.00,
            'delivery_charges' => 10.00,
            'service_charges' => 0.00,
            'total_delivery_charges' => 10.00,
            'line_delivery_charges' => 0.00,
            'applied_discount' => 10.00,
            'total_applied_discount' => 10.00,
            'order_status' => 'New',
            'order_date' => now()->format('Y-m-d'),
            'due_date' => null,
            'ship_address' => 'Test Address',
            'order_menu' => 'lunch',
            'invoice_status' => 'Unbill',
            'amount_paid' => 0,
            'inventory_type' => 'perishable',
            'food_type' => 'veg',
            'total_third_party_charges' => 0.00,
            'order_for' => 'fixed',
            'PRODUCT_MEAL_CALENDAR' => 0,
            'delivery_type' => 'delivery',
            'delivery_person' => null,
            'payment_mode' => 'online',
            'days_preference' => '1,2,3,4,5',
            'tp_delivery' => null,
            'tp_delivery_charges' => null,
            'tp_delivery_charges_type' => null,
            'tp_aggregator' => null,
            'tp_aggregator_charges' => null,
            'tp_aggregator_charges_type' => null,
            'tax_method' => 'exclusive',
            'apply_tax' => 'yes',
            'source' => 'api',
            'delivery_time' => '12:00:00',
            'delivery_end_time' => '13:00:00',
            'item_preference' => null,
            'recurring_status' => '1',
            'last_modified' => now(),
        ]);

        // Get the temp order
        $tempPreOrder = DB::table('temp_pre_orders')->where('pk_order_no', $tempPreOrderId)->first();
        
        // Create a mock request
        $request = new Request();
        
        // Create controller instance and call the protected method
        $controller = new OrderManagementController();
        
        // Use reflection to access the protected method
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('createActualOrdersFromTemp');
        $method->setAccessible(true);
        
        // Call the method
        $createdOrders = $method->invoke($controller, $tempPreOrder, $request);
        
        // Verify that orders were created
        $this->assertNotEmpty($createdOrders);
        
        // Get the created order
        $createdOrder = DB::table('orders')->where('ref_order', $tempPreOrderId)->first();
        
        // Assert that the order was created
        $this->assertNotNull($createdOrder);
        
        // Assert that promo codes were transferred correctly
        $this->assertEquals('TESTPROMO', $createdOrder->promo_code, 'User promo code should be transferred');
        $this->assertEquals('SYSTEM10', $createdOrder->system_promo_code, 'System promo code should be transferred');
        
        // Assert that other related fields were transferred correctly
        $this->assertEquals(10.00, $createdOrder->applied_discount, 'Applied discount should be transferred');
        $this->assertEquals(90.00, $createdOrder->amount, 'Amount should be transferred');
        $this->assertEquals(100.00, $createdOrder->product_price, 'Product price should be transferred');
        
        // Assert that the order references the temp order
        $this->assertEquals($tempPreOrderId, $createdOrder->ref_order, 'Order should reference temp pre-order');
    }

    /**
     * Test that null promo codes are handled correctly
     */
    public function test_null_promo_code_transfer()
    {
        // Create a mock temp_pre_order without promo codes
        $tempPreOrderId = DB::table('temp_pre_orders')->insertGetId([
            'company_id' => 8163,
            'unit_id' => 8163,
            'fk_kitchen_code' => 1,
            'ref_order' => 0,
            'order_no' => 'TESTNULL' . time(),
            'auth_id' => 3800,
            'customer_code' => 3800,
            'customer_name' => 'Test Customer',
            'food_preference' => '[]',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
            'location_code' => 1,
            'location_name' => 'Test Location',
            'city' => 9,
            'city_name' => 'Test City',
            'product_code' => 342,
            'product_name' => 'Test Meal',
            'product_description' => 'Test meal description',
            'product_type' => 'Meal',
            'quantity' => 1,
            'order_type' => 'Day',
            'order_days' => '2025-09-09',
            'promo_code' => null, // No promo code
            'system_promo_code' => null, // No system promo code
            'product_price' => 100.00,
            'amount' => 100.00, // No discount
            'total_amt' => 100.00,
            'tax' => 10.00,
            'total_tax' => 10.00,
            'delivery_charges' => 10.00,
            'service_charges' => 0.00,
            'total_delivery_charges' => 10.00,
            'line_delivery_charges' => 0.00,
            'applied_discount' => 0.00,
            'total_applied_discount' => 0.00,
            'order_status' => 'New',
            'order_date' => now()->format('Y-m-d'),
            'due_date' => null,
            'ship_address' => 'Test Address',
            'order_menu' => 'lunch',
            'invoice_status' => 'Unbill',
            'amount_paid' => 0,
            'inventory_type' => 'perishable',
            'food_type' => 'veg',
            'total_third_party_charges' => 0.00,
            'order_for' => 'fixed',
            'PRODUCT_MEAL_CALENDAR' => 0,
            'delivery_type' => 'delivery',
            'delivery_person' => null,
            'payment_mode' => 'online',
            'days_preference' => '1,2,3,4,5',
            'tp_delivery' => null,
            'tp_delivery_charges' => null,
            'tp_delivery_charges_type' => null,
            'tp_aggregator' => null,
            'tp_aggregator_charges' => null,
            'tp_aggregator_charges_type' => null,
            'tax_method' => 'exclusive',
            'apply_tax' => 'yes',
            'source' => 'api',
            'delivery_time' => '12:00:00',
            'delivery_end_time' => '13:00:00',
            'item_preference' => null,
            'recurring_status' => '1',
            'last_modified' => now(),
        ]);

        // Get the temp order
        $tempPreOrder = DB::table('temp_pre_orders')->where('pk_order_no', $tempPreOrderId)->first();
        
        // Create a mock request
        $request = new Request();
        
        // Create controller instance and call the protected method
        $controller = new OrderManagementController();
        
        // Use reflection to access the protected method
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('createActualOrdersFromTemp');
        $method->setAccessible(true);
        
        // Call the method
        $createdOrders = $method->invoke($controller, $tempPreOrder, $request);
        
        // Verify that orders were created
        $this->assertNotEmpty($createdOrders);
        
        // Get the created order
        $createdOrder = DB::table('orders')->where('ref_order', $tempPreOrderId)->first();
        
        // Assert that the order was created
        $this->assertNotNull($createdOrder);
        
        // Assert that null promo codes were transferred correctly
        $this->assertNull($createdOrder->promo_code, 'Null promo code should remain null');
        $this->assertNull($createdOrder->system_promo_code, 'Null system promo code should remain null');
        
        // Assert that other fields were transferred correctly
        $this->assertEquals(0.00, $createdOrder->applied_discount, 'Applied discount should be 0');
        $this->assertEquals(100.00, $createdOrder->amount, 'Amount should be transferred');
    }
}
