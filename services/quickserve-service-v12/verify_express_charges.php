<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== EXPRESS DELIVERY CHARGE VERIFICATION ===" . PHP_EOL;
echo PHP_EOL;

try {
    // Test 1: Verify express settings exist
    echo "1. Checking Express Settings:" . PHP_EOL;
    $expressSettings = DB::table('settings')
        ->where('key', 'LIKE', '%EXPRESS%')
        ->get(['key', 'value']);

    foreach ($expressSettings as $setting) {
        echo "   {$setting->key}: {$setting->value}" . PHP_EOL;
    }
    echo PHP_EOL;

    // Test 2: Test getExpressExtraDeliveryCharge method
    echo "2. Testing getExpressExtraDeliveryCharge Method:" . PHP_EOL;
    
    $controller = new \App\Http\Controllers\Api\V2\OrderManagementController();
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('getExpressExtraDeliveryCharge');
    $method->setAccessible(true);

    // Test lunch express charge
    $lunchCharge = $method->invoke($controller, 1, 'lunch', 8163);
    echo "   Kitchen 1 Lunch Express Charge: ₹{$lunchCharge}" . PHP_EOL;

    // Test breakfast express charge
    $breakfastCharge = $method->invoke($controller, 1, 'breakfast', 8163);
    echo "   Kitchen 1 Breakfast Express Charge: ₹{$breakfastCharge}" . PHP_EOL;
    echo PHP_EOL;

    // Test 3: Business Logic Calculation
    echo "3. Business Logic Verification:" . PHP_EOL;
    
    $mealAmountPerDay = 75.00;
    $taxPerDay = 3.75; // 5% tax
    $deliveryDaysCount = 5;
    $expressChargePerDay = $lunchCharge;

    echo "   Meal Amount per Day: ₹{$mealAmountPerDay}" . PHP_EOL;
    echo "   Tax per Day (5%): ₹{$taxPerDay}" . PHP_EOL;
    echo "   Delivery Days: {$deliveryDaysCount} days" . PHP_EOL;
    echo "   Express Charge per Day: ₹{$expressChargePerDay}" . PHP_EOL;
    echo PHP_EOL;

    $baseDailyAmount = $mealAmountPerDay + $taxPerDay;
    $baseTotal = $baseDailyAmount * $deliveryDaysCount;
    $totalWithExpress = $baseTotal + $expressChargePerDay;

    echo "   Base Daily Amount (meal + tax): ₹{$baseDailyAmount}" . PHP_EOL;
    echo "   Base Total (5 days): ₹{$baseTotal}" . PHP_EOL;
    echo "   Total with Express (base + express for 1 day): ₹{$totalWithExpress}" . PHP_EOL;
    echo PHP_EOL;

    // Test 4: Verify recent payment transactions
    echo "4. Recent Payment Transactions:" . PHP_EOL;
    $recentTransactions = DB::table('payment_transaction')
        ->select('pre_order_id', 'payment_amount', 'transaction_charges', 'created_date')
        ->orderBy('pk_transaction_id', 'desc')
        ->limit(3)
        ->get();

    foreach ($recentTransactions as $tx) {
        $totalAmount = (float)$tx->payment_amount + (float)$tx->transaction_charges;
        echo "   Order: {$tx->pre_order_id} | Payment: ₹{$tx->payment_amount} | Charges: ₹{$tx->transaction_charges} | Total: ₹{$totalAmount}" . PHP_EOL;
    }
    echo PHP_EOL;

    // Test 5: Check if method name fix worked
    echo "5. Method Name Fix Verification:" . PHP_EOL;
    
    // Check if the old method exists (should not)
    $hasOldMethod = $reflection->hasMethod('getExpressExtraCharge');
    echo "   Old method 'getExpressExtraCharge' exists: " . ($hasOldMethod ? "❌ YES (should be removed)" : "✅ NO (correct)") . PHP_EOL;
    
    // Check if the correct method exists (should exist)
    $hasNewMethod = $reflection->hasMethod('getExpressExtraDeliveryCharge');
    echo "   New method 'getExpressExtraDeliveryCharge' exists: " . ($hasNewMethod ? "✅ YES (correct)" : "❌ NO (problem)") . PHP_EOL;
    echo PHP_EOL;

    echo "✅ EXPRESS DELIVERY CHARGE VERIFICATION COMPLETE" . PHP_EOL;
    echo PHP_EOL;

    // Summary
    echo "📋 SUMMARY:" . PHP_EOL;
    echo "   - Method name fix: " . ($hasNewMethod && !$hasOldMethod ? "✅ FIXED" : "❌ NEEDS ATTENTION") . PHP_EOL;
    echo "   - Express charges configured: " . ($lunchCharge > 0 || $breakfastCharge > 0 ? "✅ YES" : "❌ NO") . PHP_EOL;
    echo "   - Business logic: Express charges apply only to current day" . PHP_EOL;
    echo "   - Payment calculation: Base amount × days + express charge (1 day only)" . PHP_EOL;

} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . PHP_EOL;
    echo "Stack trace: " . $e->getTraceAsString() . PHP_EOL;
}
