<?php

namespace App\Listeners;

use App\Events\OrderDeliveredEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderDeliveredListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     * When an order is delivered, process wallet deduction for the meal amount
     */
    public function handle(OrderDeliveredEvent $event): void
    {
        try {
            $orderId = $event->orderId;
            
            // Get order details
            $order = DB::table('orders')
                ->where('pk_order_no', $orderId)
                ->first();

            if (!$order) {
                Log::warning('Order not found for delivery processing', [
                    'order_id' => $orderId
                ]);
                return;
            }

            // Check if order was paid via wallet (amount_paid = 1 indicates payment was made)
            if ($order->amount_paid != 1) {
                Log::info('Order not paid via wallet, skipping wallet deduction', [
                    'order_id' => $orderId,
                    'order_no' => $order->order_no,
                    'amount_paid' => $order->amount_paid
                ]);
                return;
            }

            // Process wallet deduction for delivered order
            $this->processWalletDeductionForDeliveredOrder($order);

            Log::info('Order delivered event processed successfully', [
                'order_id' => $orderId,
                'order_no' => $order->order_no,
                'customer_code' => $order->customer_code,
                'net_amount' => $order->net_amount
            ]);

        } catch (\Exception $e) {
            Log::error('Error processing order delivered event', [
                'order_id' => $event->orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Re-throw to trigger retry mechanism
            throw $e;
        }
    }

    /**
     * Process wallet deduction for delivered order
     * Creates a debit entry in customer_wallet table
     *
     * @param object $order
     * @return void
     */
    protected function processWalletDeductionForDeliveredOrder(object $order): void
    {
        try {
            // Check if wallet deduction already exists for this order
            $existingDeduction = DB::table('customer_wallet')
                ->where('fk_customer_code', $order->customer_code)
                ->where('reference_no', $order->order_no)
                ->where('amount_type', 'dr')
                ->where('description', 'LIKE', '%delivered%')
                ->exists();

            if ($existingDeduction) {
                Log::info('Wallet deduction already exists for delivered order', [
                    'order_no' => $order->order_no,
                    'customer_code' => $order->customer_code
                ]);
                return;
            }

            // Calculate deduction amount (net amount excluding delivery charges)
            $deductionAmount = $order->net_amount - ($order->delivery_charges ?? 0);
            
            if ($deductionAmount <= 0) {
                Log::warning('Invalid deduction amount for delivered order', [
                    'order_no' => $order->order_no,
                    'net_amount' => $order->net_amount,
                    'delivery_charges' => $order->delivery_charges ?? 0,
                    'calculated_deduction' => $deductionAmount
                ]);
                return;
            }

            // Create wallet debit entry for delivered order
            DB::table('customer_wallet')->insert([
                'company_id' => $order->company_id,
                'unit_id' => $order->unit_id ?? $order->company_id,
                'fk_customer_code' => $order->customer_code,
                'wallet_amount' => $deductionAmount,
                'amount_type' => 'dr', // Debit
                'reference_no' => $order->order_no,
                'payment_type' => 'wallet',
                'payment_date' => now()->toDateString(),
                'description' => "₹{$deductionAmount} deducted for delivered order {$order->order_no} (Bill No. {$order->pk_order_no})",
                'created_by' => $order->customer_code,
                'created_date' => now(),
                'updated_by' => $order->customer_code,
                'updated_date' => now(),
                'context' => 'customer',
            ]);

            Log::info('Wallet deduction processed for delivered order', [
                'order_no' => $order->order_no,
                'customer_code' => $order->customer_code,
                'deduction_amount' => $deductionAmount,
                'net_amount' => $order->net_amount,
                'delivery_charges' => $order->delivery_charges ?? 0
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to process wallet deduction for delivered order', [
                'order_no' => $order->order_no,
                'customer_code' => $order->customer_code,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }
}
