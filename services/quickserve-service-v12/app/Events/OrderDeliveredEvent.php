<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderDeliveredEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The order ID that was delivered
     *
     * @var int
     */
    public int $orderId;

    /**
     * The delivery person ID who delivered the order
     *
     * @var int
     */
    public int $deliveryPersonId;

    /**
     * Create a new event instance.
     *
     * @param int $orderId
     * @param int $deliveryPersonId
     */
    public function __construct(int $orderId, int $deliveryPersonId)
    {
        $this->orderId = $orderId;
        $this->deliveryPersonId = $deliveryPersonId;
    }
}
