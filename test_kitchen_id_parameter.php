<?php

/**
 * Test script to verify kitchen_id parameter handling
 * Tests both with and without kitchen_id parameter
 */

$baseUrl = 'http://*************:8000/api/v2/admin/settings/payment-gateways';
$companyId = 8163;

echo "🧪 Testing Kitchen ID Parameter Handling\n";
echo "=" . str_repeat("=", 50) . "\n\n";

/**
 * Test 1: Without kitchen_id parameter (should default to 1)
 */
echo "Test 1: Without kitchen_id parameter\n";
echo "URL: {$baseUrl}?company_id={$companyId}\n";

$ch1 = curl_init();
curl_setopt($ch1, CURLOPT_URL, "{$baseUrl}?company_id={$companyId}");
curl_setopt($ch1, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch1, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);

$response1 = curl_exec($ch1);
$httpCode1 = curl_getinfo($ch1, CURLINFO_HTTP_CODE);
curl_close($ch1);

echo "HTTP Code: {$httpCode1}\n";
if ($httpCode1 === 200) {
    $data1 = json_decode($response1, true);
    echo "✅ Success - Default kitchen_id handling works\n";
    echo "Response contains k1_wallet_enabled: " . (isset($data1['data']['extra']['k1_wallet_enabled']) ? 'Yes' : 'No') . "\n";
    if (isset($data1['data']['extra']['wallet_debug'])) {
        echo "Kitchen payment modes: " . ($data1['data']['extra']['wallet_debug']['kitchen_payment_modes'] ?? 'N/A') . "\n";
    }
} else {
    echo "❌ Failed - HTTP {$httpCode1}\n";
    echo "Response: " . substr($response1, 0, 200) . "...\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

/**
 * Test 2: With kitchen_id parameter = 1
 */
echo "Test 2: With kitchen_id=1 parameter\n";
echo "URL: {$baseUrl}?company_id={$companyId}&kitchen_id=1\n";

$ch2 = curl_init();
curl_setopt($ch2, CURLOPT_URL, "{$baseUrl}?company_id={$companyId}&kitchen_id=1");
curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch2, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);

$response2 = curl_exec($ch2);
$httpCode2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
curl_close($ch2);

echo "HTTP Code: {$httpCode2}\n";
if ($httpCode2 === 200) {
    $data2 = json_decode($response2, true);
    echo "✅ Success - Explicit kitchen_id=1 works\n";
    echo "Response contains k1_wallet_enabled: " . (isset($data2['data']['extra']['k1_wallet_enabled']) ? 'Yes' : 'No') . "\n";
    if (isset($data2['data']['extra']['wallet_debug'])) {
        echo "Kitchen payment modes: " . ($data2['data']['extra']['wallet_debug']['kitchen_payment_modes'] ?? 'N/A') . "\n";
    }
} else {
    echo "❌ Failed - HTTP {$httpCode2}\n";
    echo "Response: " . substr($response2, 0, 200) . "...\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

/**
 * Test 3: With kitchen_id parameter = 2 (if exists)
 */
echo "Test 3: With kitchen_id=2 parameter\n";
echo "URL: {$baseUrl}?company_id={$companyId}&kitchen_id=2\n";

$ch3 = curl_init();
curl_setopt($ch3, CURLOPT_URL, "{$baseUrl}?company_id={$companyId}&kitchen_id=2");
curl_setopt($ch3, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch3, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);

$response3 = curl_exec($ch3);
$httpCode3 = curl_getinfo($ch3, CURLINFO_HTTP_CODE);
curl_close($ch3);

echo "HTTP Code: {$httpCode3}\n";
if ($httpCode3 === 200) {
    $data3 = json_decode($response3, true);
    echo "✅ Success - kitchen_id=2 works\n";
    echo "Response contains k2_wallet_enabled: " . (isset($data3['data']['extra']['k2_wallet_enabled']) ? 'Yes' : 'No') . "\n";
    if (isset($data3['data']['extra']['wallet_debug'])) {
        echo "Kitchen payment modes: " . ($data3['data']['extra']['wallet_debug']['kitchen_payment_modes'] ?? 'N/A') . "\n";
    }
} else {
    echo "❌ Failed - HTTP {$httpCode3}\n";
    echo "Response: " . substr($response3, 0, 200) . "...\n";
}

echo "\n" . str_repeat("=", 50) . "\n";

/**
 * Summary
 */
echo "📊 Test Summary:\n";
echo "- Test 1 (No kitchen_id): " . ($httpCode1 === 200 ? "✅ PASS" : "❌ FAIL") . "\n";
echo "- Test 2 (kitchen_id=1): " . ($httpCode2 === 200 ? "✅ PASS" : "❌ FAIL") . "\n";
echo "- Test 3 (kitchen_id=2): " . ($httpCode3 === 200 ? "✅ PASS" : "❌ FAIL") . "\n";

if ($httpCode1 === 200 && $httpCode2 === 200) {
    echo "\n🎉 Kitchen ID parameter handling is working correctly!\n";
    echo "✅ Default value (1) is applied when kitchen_id is not provided\n";
    echo "✅ No 'Undefined array key' errors occur\n";
    echo "✅ OpenAPI specification updated with kitchen_id parameter\n";
} else {
    echo "\n⚠️  Some tests failed. Please check the implementation.\n";
}

echo "\n";
