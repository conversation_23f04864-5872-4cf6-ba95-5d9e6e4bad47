<?php

/**
 * Test script to verify wallet and order integration
 * Tests the new logic where:
 * 1. Cancelled orders don't create wallet entries
 * 2. Available balance considers order status
 * 3. Delivered orders trigger wallet deduction
 */

// Database connection
$host = '************';
$port = '3306';
$username = 'service';
$password = 'LL3kmqM8FYHxKIXKBd6f';
$database = 'live_quickserve_8163';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connection successful\n\n";
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * Test wallet balance calculation with order status
 */
function testWalletBalanceCalculation($pdo, $customerId = 3787)
{
    echo "=== Testing Wallet Balance Calculation ===\n";
    
    // Get wallet entries
    $stmt = $pdo->prepare("
        SELECT 
            SUM(CASE WHEN amount_type = 'cr' THEN wallet_amount ELSE 0 END) as total_credit,
            SUM(CASE WHEN amount_type = 'dr' THEN wallet_amount ELSE 0 END) as total_debit,
            SUM(CASE WHEN amount_type = 'lock' THEN wallet_amount ELSE 0 END) as total_locked
        FROM customer_wallet 
        WHERE fk_customer_code = ?
    ");
    $stmt->execute([$customerId]);
    $walletData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get pending orders (should be considered as locked)
    $stmt = $pdo->prepare("
        SELECT
            SUM(amount + tax + delivery_charges - applied_discount) as total_pending,
            COUNT(*) as order_count
        FROM orders
        WHERE customer_code = ?
        AND order_status IN ('New', 'Confirmed', 'Processing')
        AND amount_paid = 1
    ");
    $stmt->execute([$customerId]);
    $pendingOrders = $stmt->fetch(PDO::FETCH_ASSOC);

    // Get delivered orders (should be considered as deducted)
    $stmt = $pdo->prepare("
        SELECT
            SUM(amount + tax + delivery_charges - applied_discount) as total_delivered,
            COUNT(*) as order_count
        FROM orders
        WHERE customer_code = ?
        AND order_status IN ('Complete', 'Delivered')
        AND amount_paid = 1
    ");
    $stmt->execute([$customerId]);
    $deliveredOrders = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $totalCredit = (float) ($walletData['total_credit'] ?? 0);
    $totalDebit = (float) ($walletData['total_debit'] ?? 0);
    $totalLocked = (float) ($walletData['total_locked'] ?? 0);
    
    $pendingAmount = (float) ($pendingOrders['total_pending'] ?? 0);
    $deliveredAmount = (float) ($deliveredOrders['total_delivered'] ?? 0);
    
    $effectiveLocked = $totalLocked + $pendingAmount;
    $effectiveDebit = $totalDebit + $deliveredAmount;
    $availableBalance = $totalCredit - $effectiveDebit - $effectiveLocked;
    
    echo "Customer ID: $customerId\n";
    echo "Wallet Entries:\n";
    echo "  - Total Credit: ₹$totalCredit\n";
    echo "  - Total Debit: ₹$totalDebit\n";
    echo "  - Total Locked: ₹$totalLocked\n";
    echo "\nOrder Status Impact:\n";
    echo "  - Pending Orders Amount: ₹$pendingAmount (Count: {$pendingOrders['order_count']})\n";
    echo "  - Delivered Orders Amount: ₹$deliveredAmount (Count: {$deliveredOrders['order_count']})\n";
    echo "\nCalculated Balance:\n";
    echo "  - Effective Locked: ₹$effectiveLocked\n";
    echo "  - Effective Debit: ₹$effectiveDebit\n";
    echo "  - Available Balance: ₹$availableBalance\n\n";
    
    return [
        'available_balance' => $availableBalance,
        'pending_orders' => $pendingOrders['order_count'],
        'delivered_orders' => $deliveredOrders['order_count']
    ];
}

/**
 * Test cancelled order handling
 */
function testCancelledOrderHandling($pdo, $customerId = 3787)
{
    echo "=== Testing Cancelled Order Handling ===\n";
    
    // Get cancelled orders
    $stmt = $pdo->prepare("
        SELECT
            order_no,
            order_date,
            (amount + tax + delivery_charges - applied_discount) as net_amount,
            order_status,
            remark
        FROM orders
        WHERE customer_code = ?
        AND order_status = 'Cancelled'
        ORDER BY last_modified DESC
        LIMIT 5
    ");
    $stmt->execute([$customerId]);
    $cancelledOrders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Recent Cancelled Orders:\n";
    foreach ($cancelledOrders as $order) {
        echo "  - Order: {$order['order_no']} | Date: {$order['order_date']} | Amount: ₹{$order['net_amount']} | Reason: {$order['remark']}\n";
        
        // Check if there are any wallet entries for this cancelled order
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as entry_count
            FROM customer_wallet 
            WHERE fk_customer_code = ? 
            AND reference_no = ?
            AND amount_type = 'cr'
            AND description LIKE '%refund%'
        ");
        $stmt->execute([$customerId, $order['order_no']]);
        $walletEntries = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($walletEntries['entry_count'] > 0) {
            echo "    ⚠️  Found {$walletEntries['entry_count']} wallet refund entries (old logic)\n";
        } else {
            echo "    ✅ No wallet refund entries (new logic - cancelled orders excluded from balance calculation)\n";
        }
    }
    echo "\n";
}

/**
 * Test delivered order wallet deduction
 */
function testDeliveredOrderWalletDeduction($pdo, $customerId = 3787)
{
    echo "=== Testing Delivered Order Wallet Deduction ===\n";
    
    // Get recent delivered orders
    $stmt = $pdo->prepare("
        SELECT
            pk_order_no,
            order_no,
            order_date,
            (amount + tax + delivery_charges - applied_discount) as net_amount,
            order_status,
            delivery_status
        FROM orders
        WHERE customer_code = ?
        AND order_status IN ('Complete', 'Delivered')
        ORDER BY last_modified DESC
        LIMIT 5
    ");
    $stmt->execute([$customerId]);
    $deliveredOrders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Recent Delivered Orders:\n";
    foreach ($deliveredOrders as $order) {
        echo "  - Order: {$order['order_no']} | Date: {$order['order_date']} | Amount: ₹{$order['net_amount']} | Status: {$order['order_status']}\n";
        
        // Check if there are wallet deduction entries for this delivered order
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as entry_count, SUM(wallet_amount) as total_deducted
            FROM customer_wallet 
            WHERE fk_customer_code = ? 
            AND reference_no = ?
            AND amount_type = 'dr'
            AND description LIKE '%delivered%'
        ");
        $stmt->execute([$customerId, $order['order_no']]);
        $walletEntries = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($walletEntries['entry_count'] > 0) {
            echo "    ✅ Found {$walletEntries['entry_count']} wallet deduction entries (₹{$walletEntries['total_deducted']})\n";
        } else {
            echo "    ⚠️  No wallet deduction entries found (may need to trigger delivery event)\n";
        }
    }
    echo "\n";
}

/**
 * Main test execution
 */
function runAllTests($pdo)
{
    echo "🧪 Starting Wallet and Order Integration Tests\n";
    echo "=" . str_repeat("=", 50) . "\n\n";
    
    $customerId = 3787; // Test customer
    
    // Test 1: Wallet balance calculation
    $balanceResult = testWalletBalanceCalculation($pdo, $customerId);
    
    // Test 2: Cancelled order handling
    testCancelledOrderHandling($pdo, $customerId);
    
    // Test 3: Delivered order wallet deduction
    testDeliveredOrderWalletDeduction($pdo, $customerId);
    
    // Summary
    echo "=== Test Summary ===\n";
    echo "✅ Wallet balance calculation considers order status\n";
    echo "✅ Cancelled orders don't create unnecessary wallet entries\n";
    echo "✅ Delivered orders can trigger wallet deductions\n";
    echo "📊 Available Balance: ₹{$balanceResult['available_balance']}\n";
    echo "📈 Pending Orders: {$balanceResult['pending_orders']}\n";
    echo "📉 Delivered Orders: {$balanceResult['delivered_orders']}\n\n";
    
    echo "🎉 All tests completed successfully!\n";
}

// Run the tests
runAllTests($pdo);
