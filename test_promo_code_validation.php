<?php

/**
 * Test script to verify promo code validation and discount calculation
 * Tests various promo code scenarios in order creation
 */

$baseUrl = 'http://*************:8003/api/v2/order-management/orders';
$validPayload = [
    'customer_id' => 3800,
    'customer_address' => '123 Test Street, Test Area',
    'location_code' => 1,
    'city' => 9,
    'company_id' => 8163,
    'unit_id' => 8163,
    'fk_kitchen_code' => 1,
    'meals' => [
        ['product_code' => 342, 'quantity' => 1]
    ],
    'start_date' => date('Y-m-d', strtotime('+1 day')),
    'selected_days' => [1, 2, 3, 4, 5],
    'subscription_days' => 5,
    'delivery_time' => '08:00:00',
    'delivery_end_time' => '09:00:00',
    'food_preference' => 'veg'
];

echo "🎫 Testing Promo Code Functionality\n";
echo "=" . str_repeat("=", 50) . "\n\n";

/**
 * Test 1: Order without promo code (should succeed)
 */
echo "Test 1: Order without promo code\n";
$response1 = makeRequest($baseUrl, $validPayload);
echo "HTTP Code: {$response1['http_code']}\n";
if ($response1['http_code'] === 201) {
    echo "✅ Success - Order created without promo code\n";
    $data = json_decode($response1['response'], true);
    if (isset($data['data']['order_no'])) {
        echo "Order created: " . $data['data']['order_no'] . "\n";
        echo "Total amount: ₹" . ($data['data']['total_amount'] ?? 'N/A') . "\n";
    }
} else {
    echo "❌ Failed - Order without promo code rejected\n";
    echo "Response: " . substr($response1['response'], 0, 200) . "...\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

/**
 * Test 2: Order with promo code applied=false (should succeed, no discount)
 */
echo "Test 2: Order with promo code applied=false\n";
$payloadWithPromoFalse = $validPayload;
$payloadWithPromoFalse['promo_code'] = [
    'applied' => false,
    'code' => 'SPECIAL'
];

$response2 = makeRequest($baseUrl, $payloadWithPromoFalse);
echo "HTTP Code: {$response2['http_code']}\n";
if ($response2['http_code'] === 201) {
    echo "✅ Success - Order created with promo code applied=false\n";
    $data = json_decode($response2['response'], true);
    if (isset($data['data']['order_no'])) {
        echo "Order created: " . $data['data']['order_no'] . "\n";
        echo "Total amount: ₹" . ($data['data']['total_amount'] ?? 'N/A') . "\n";
    }
} else {
    echo "❌ Failed - Order with promo code applied=false rejected\n";
    echo "Response: " . substr($response2['response'], 0, 200) . "...\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

/**
 * Test 3: Order with valid promo code (should succeed with discount)
 */
echo "Test 3: Order with valid promo code\n";
$payloadWithValidPromo = $validPayload;
$payloadWithValidPromo['promo_code'] = [
    'applied' => true,
    'code' => 'SPECIAL' // Assuming this exists in database
];

$response3 = makeRequest($baseUrl, $payloadWithValidPromo);
echo "HTTP Code: {$response3['http_code']}\n";
if ($response3['http_code'] === 201) {
    echo "✅ Success - Order created with valid promo code\n";
    $data = json_decode($response3['response'], true);
    if (isset($data['data']['order_no'])) {
        echo "Order created: " . $data['data']['order_no'] . "\n";
        echo "Total amount: ₹" . ($data['data']['total_amount'] ?? 'N/A') . "\n";
        if (isset($data['data']['promo_discount'])) {
            echo "Promo discount: ₹" . $data['data']['promo_discount'] . "\n";
        }
    }
} else {
    echo "❌ Failed - Order with valid promo code rejected\n";
    echo "Response: " . substr($response3['response'], 0, 300) . "...\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

/**
 * Test 4: Order with invalid promo code (should fail with 422)
 */
echo "Test 4: Order with invalid promo code\n";
$payloadWithInvalidPromo = $validPayload;
$payloadWithInvalidPromo['promo_code'] = [
    'applied' => true,
    'code' => 'INVALID123'
];

$response4 = makeRequest($baseUrl, $payloadWithInvalidPromo);
echo "HTTP Code: {$response4['http_code']}\n";
if ($response4['http_code'] === 422) {
    echo "✅ Success - Invalid promo code rejected with 422\n";
    $data = json_decode($response4['response'], true);
    if (isset($data['error_code']) && $data['error_code'] === 'INVALID_PROMO_CODE') {
        echo "Error code: " . $data['error_code'] . "\n";
        echo "Message: " . $data['message'] . "\n";
    }
} else {
    echo "❌ Failed - Invalid promo code not properly rejected\n";
    echo "Response: " . substr($response4['response'], 0, 300) . "...\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

/**
 * Test 5: Order with promo code applied=true but no code (should fail validation)
 */
echo "Test 5: Order with promo code applied=true but no code\n";
$payloadWithMissingCode = $validPayload;
$payloadWithMissingCode['promo_code'] = [
    'applied' => true
    // Missing 'code' field
];

$response5 = makeRequest($baseUrl, $payloadWithMissingCode);
echo "HTTP Code: {$response5['http_code']}\n";
if ($response5['http_code'] === 422) {
    echo "✅ Success - Missing promo code rejected with validation error\n";
    $data = json_decode($response5['response'], true);
    if (isset($data['message'])) {
        echo "Message: " . $data['message'] . "\n";
    }
} else {
    echo "❌ Failed - Missing promo code not properly rejected\n";
    echo "Response: " . substr($response5['response'], 0, 300) . "...\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

/**
 * Test 6: Order with expired promo code (should fail with 422)
 */
echo "Test 6: Order with expired promo code\n";
$payloadWithExpiredPromo = $validPayload;
$payloadWithExpiredPromo['promo_code'] = [
    'applied' => true,
    'code' => 'EXPIRED2023' // Assuming this is an expired code
];

$response6 = makeRequest($baseUrl, $payloadWithExpiredPromo);
echo "HTTP Code: {$response6['http_code']}\n";
if ($response6['http_code'] === 422) {
    echo "✅ Success - Expired promo code rejected with 422\n";
    $data = json_decode($response6['response'], true);
    if (isset($data['error_code']) && $data['error_code'] === 'INVALID_PROMO_CODE') {
        echo "Error code: " . $data['error_code'] . "\n";
        echo "Message: " . $data['message'] . "\n";
    }
} else {
    echo "❌ Failed - Expired promo code not properly rejected\n";
    echo "Response: " . substr($response6['response'], 0, 300) . "...\n";
}

echo "\n" . str_repeat("=", 50) . "\n";

/**
 * Summary
 */
echo "📊 Test Summary:\n";
echo "- Test 1 (No promo code): " . ($response1['http_code'] === 201 ? "✅ PASS" : "❌ FAIL") . "\n";
echo "- Test 2 (Promo applied=false): " . ($response2['http_code'] === 201 ? "✅ PASS" : "❌ FAIL") . "\n";
echo "- Test 3 (Valid promo code): " . ($response3['http_code'] === 201 ? "✅ PASS" : "❌ FAIL") . "\n";
echo "- Test 4 (Invalid promo code): " . ($response4['http_code'] === 422 ? "✅ PASS" : "❌ FAIL") . "\n";
echo "- Test 5 (Missing code field): " . ($response5['http_code'] === 422 ? "✅ PASS" : "❌ FAIL") . "\n";
echo "- Test 6 (Expired promo code): " . ($response6['http_code'] === 422 ? "✅ PASS" : "❌ FAIL") . "\n";

$passCount = 0;
if ($response1['http_code'] === 201) $passCount++;
if ($response2['http_code'] === 201) $passCount++;
if ($response3['http_code'] === 201) $passCount++;
if ($response4['http_code'] === 422) $passCount++;
if ($response5['http_code'] === 422) $passCount++;
if ($response6['http_code'] === 422) $passCount++;

if ($passCount >= 5) {
    echo "\n🎉 Promo code functionality is working correctly!\n";
    echo "✅ Orders without promo codes work\n";
    echo "✅ Promo code validation is working\n";
    echo "✅ Invalid/expired codes are rejected\n";
    echo "✅ Discount calculation is applied\n";
} else {
    echo "\n⚠️  Some tests failed. Please check the implementation.\n";
    echo "Passed: {$passCount}/6 tests\n";
}

echo "\n";

/**
 * Helper function to make HTTP requests
 */
function makeRequest($url, $payload) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'response' => $response,
        'http_code' => $httpCode
    ];
}
